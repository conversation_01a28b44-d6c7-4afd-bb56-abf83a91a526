"""
Do not import this file directly. See `types.py` for the classes that have a stable API.

Auto-generated file -- do not modify.
"""

from __future__ import annotations

from typing import Any, Literal, Mapping, Optional, Sequence, TypedDict, Union

from typing_extensions import NotRequired

SpanType = Literal["llm", "score", "function", "eval", "task", "tool"]


class ObjectReference(TypedDict):
    object_type: Union[
        Literal["experiment", "dataset", "prompt", "function", "prompt_session"],
        Literal["project_logs"],
    ]
    """
    Type of the object the event is originating from.
    """
    object_id: str
    """
    ID of the object the event is originating from.
    """
    id: str
    """
    ID of the original event.
    """
    _xact_id: NotRequired[Optional[str]]
    """
    Transaction ID of the original event.
    """
    created: NotRequired[Optional[str]]
    """
    Created timestamp of the original event. Used to help sort in the UI
    """


class Metadata(TypedDict):
    model: NotRequired[Optional[str]]
    """
    The model used for this example
    """


class DatasetEvent(TypedDict):
    id: str
    """
    A unique identifier for the dataset event. If you don't provide one, Braintrust will generate one for you
    """
    _xact_id: str
    """
    The transaction id of an event is unique to the network operation that processed the event insertion. Transaction ids are monotonically increasing over time and can be used to retrieve a versioned snapshot of the dataset (see the `version` parameter)
    """
    created: str
    """
    The timestamp the dataset event was created
    """
    _pagination_key: NotRequired[Optional[str]]
    """
    A stable, time-ordered key that can be used to paginate over dataset events. This field is auto-generated by Braintrust and only exists in Brainstore.
    """
    project_id: str
    """
    Unique identifier for the project that the dataset belongs under
    """
    dataset_id: str
    """
    Unique identifier for the dataset
    """
    input: NotRequired[Optional[Any]]
    """
    The argument that uniquely define an input case (an arbitrary, JSON serializable object)
    """
    expected: NotRequired[Optional[Any]]
    """
    The output of your application, including post-processing (an arbitrary, JSON serializable object)
    """
    metadata: NotRequired[Optional[Metadata]]
    """
    A dictionary with additional data about the test example, model outputs, or just about anything else that's relevant, that you can use to help find and analyze examples later. For example, you could log the `prompt`, example's `id`, or anything else that would be useful to slice/dice later. The values in `metadata` can be any JSON-serializable type, but its keys must be strings
    """
    tags: NotRequired[Optional[Sequence[str]]]
    """
    A list of tags to log
    """
    span_id: str
    """
    A unique identifier used to link different dataset events together as part of a full trace. See the [tracing guide](https://www.braintrust.dev/docs/guides/tracing) for full details on tracing
    """
    root_span_id: str
    """
    A unique identifier for the trace this dataset event belongs to
    """
    is_root: NotRequired[Optional[bool]]
    """
    Whether this span is a root span
    """
    origin: NotRequired[Optional[ObjectReference]]
    output: NotRequired[Optional[Any]]
    """
    Deprecated.
    """


class SpanAttributes(TypedDict):
    name: NotRequired[Optional[str]]
    """
    Name of the span, for display purposes only
    """
    type: NotRequired[Optional[SpanType]]
    """
    Type of the span, for display purposes only
    """


class Metrics(TypedDict):
    start: NotRequired[Optional[float]]
    """
    A unix timestamp recording when the section of code which produced the experiment event started
    """
    end: NotRequired[Optional[float]]
    """
    A unix timestamp recording when the section of code which produced the experiment event finished
    """
    prompt_tokens: NotRequired[Optional[int]]
    """
    The number of tokens in the prompt used to generate the experiment event (only set if this is an LLM span)
    """
    completion_tokens: NotRequired[Optional[int]]
    """
    The number of tokens in the completion generated by the model (only set if this is an LLM span)
    """
    tokens: NotRequired[Optional[int]]
    """
    The total number of tokens in the input and output of the experiment event.
    """
    caller_functionname: NotRequired[Optional[Any]]
    """
    This metric is deprecated
    """
    caller_filename: NotRequired[Optional[Any]]
    """
    This metric is deprecated
    """
    caller_lineno: NotRequired[Optional[Any]]
    """
    This metric is deprecated
    """


class Context(TypedDict):
    caller_functionname: NotRequired[Optional[str]]
    """
    The function in code which created the experiment event
    """
    caller_filename: NotRequired[Optional[str]]
    """
    Name of the file in code where the experiment event was created
    """
    caller_lineno: NotRequired[Optional[int]]
    """
    Line of code where the experiment event was created
    """


class ExperimentEvent(TypedDict):
    id: str
    """
    A unique identifier for the experiment event. If you don't provide one, Braintrust will generate one for you
    """
    _xact_id: str
    """
    The transaction id of an event is unique to the network operation that processed the event insertion. Transaction ids are monotonically increasing over time and can be used to retrieve a versioned snapshot of the experiment (see the `version` parameter)
    """
    created: str
    """
    The timestamp the experiment event was created
    """
    _pagination_key: NotRequired[Optional[str]]
    """
    A stable, time-ordered key that can be used to paginate over experiment events. This field is auto-generated by Braintrust and only exists in Brainstore.
    """
    project_id: str
    """
    Unique identifier for the project that the experiment belongs under
    """
    experiment_id: str
    """
    Unique identifier for the experiment
    """
    input: NotRequired[Optional[Any]]
    """
    The arguments that uniquely define a test case (an arbitrary, JSON serializable object). Later on, Braintrust will use the `input` to know whether two test cases are the same between experiments, so they should not contain experiment-specific state. A simple rule of thumb is that if you run the same experiment twice, the `input` should be identical
    """
    output: NotRequired[Optional[Any]]
    """
    The output of your application, including post-processing (an arbitrary, JSON serializable object), that allows you to determine whether the result is correct or not. For example, in an app that generates SQL queries, the `output` should be the _result_ of the SQL query generated by the model, not the query itself, because there may be multiple valid queries that answer a single question
    """
    expected: NotRequired[Optional[Any]]
    """
    The ground truth value (an arbitrary, JSON serializable object) that you'd compare to `output` to determine if your `output` value is correct or not. Braintrust currently does not compare `output` to `expected` for you, since there are so many different ways to do that correctly. Instead, these values are just used to help you navigate your experiments while digging into analyses. However, we may later use these values to re-score outputs or fine-tune your models
    """
    error: NotRequired[Optional[Any]]
    """
    The error that occurred, if any.
    """
    scores: NotRequired[Optional[Mapping[str, Optional[float]]]]
    """
    A dictionary of numeric values (between 0 and 1) to log. The scores should give you a variety of signals that help you determine how accurate the outputs are compared to what you expect and diagnose failures. For example, a summarization app might have one score that tells you how accurate the summary is, and another that measures the word similarity between the generated and grouth truth summary. The word similarity score could help you determine whether the summarization was covering similar concepts or not. You can use these scores to help you sort, filter, and compare experiments
    """
    metadata: NotRequired[Optional[Metadata]]
    """
    A dictionary with additional data about the test example, model outputs, or just about anything else that's relevant, that you can use to help find and analyze examples later. For example, you could log the `prompt`, example's `id`, or anything else that would be useful to slice/dice later. The values in `metadata` can be any JSON-serializable type, but its keys must be strings
    """
    tags: NotRequired[Optional[Sequence[str]]]
    """
    A list of tags to log
    """
    metrics: NotRequired[Optional[Metrics]]
    """
    Metrics are numerical measurements tracking the execution of the code that produced the experiment event. Use "start" and "end" to track the time span over which the experiment event was produced
    """
    context: NotRequired[Optional[Context]]
    """
    Context is additional information about the code that produced the experiment event. It is essentially the textual counterpart to `metrics`. Use the `caller_*` attributes to track the location in code which produced the experiment event
    """
    span_id: str
    """
    A unique identifier used to link different experiment events together as part of a full trace. See the [tracing guide](https://www.braintrust.dev/docs/guides/tracing) for full details on tracing
    """
    span_parents: NotRequired[Optional[Sequence[str]]]
    """
    An array of the parent `span_ids` of this experiment event. This should be empty for the root span of a trace, and should most often contain just one parent element for subspans
    """
    root_span_id: str
    """
    A unique identifier for the trace this experiment event belongs to
    """
    span_attributes: NotRequired[Optional[SpanAttributes]]
    is_root: NotRequired[Optional[bool]]
    """
    Whether this span is a root span
    """
    origin: NotRequired[Optional[ObjectReference]]


class ResponseFormat(TypedDict):
    type: Literal["json_object"]


class JsonSchema(TypedDict):
    name: str
    description: NotRequired[Optional[str]]
    schema: NotRequired[Optional[Union[Mapping[str, Any], str]]]
    strict: NotRequired[Optional[bool]]


class ResponseFormat1(TypedDict):
    type: Literal["json_schema"]
    json_schema: JsonSchema


class ResponseFormat2(TypedDict):
    type: Literal["text"]


class Function(TypedDict):
    name: str


class ToolChoice(TypedDict):
    type: Literal["function"]
    function: Function


class FunctionCall(TypedDict):
    name: str


class ModelParams1(TypedDict):
    use_cache: NotRequired[Optional[bool]]
    temperature: NotRequired[Optional[float]]
    top_p: NotRequired[Optional[float]]
    max_tokens: NotRequired[Optional[float]]
    max_completion_tokens: NotRequired[Optional[float]]
    """
    The successor to max_tokens
    """
    frequency_penalty: NotRequired[Optional[float]]
    presence_penalty: NotRequired[Optional[float]]
    response_format: NotRequired[Optional[Union[ResponseFormat, ResponseFormat1, ResponseFormat2]]]
    tool_choice: NotRequired[Optional[Union[Literal["auto"], Literal["none"], Literal["required"], ToolChoice]]]
    function_call: NotRequired[Optional[Union[Literal["auto"], Literal["none"], FunctionCall]]]
    n: NotRequired[Optional[float]]
    stop: NotRequired[Optional[Sequence[str]]]
    reasoning_effort: NotRequired[Optional[Literal["low", "medium", "high"]]]


class ModelParams2(TypedDict):
    use_cache: NotRequired[Optional[bool]]
    max_tokens: float
    temperature: float
    top_p: NotRequired[Optional[float]]
    top_k: NotRequired[Optional[float]]
    stop_sequences: NotRequired[Optional[Sequence[str]]]
    max_tokens_to_sample: NotRequired[Optional[float]]
    """
    This is a legacy parameter that should not be used.
    """


class ModelParams3(TypedDict):
    use_cache: NotRequired[Optional[bool]]
    temperature: NotRequired[Optional[float]]
    maxOutputTokens: NotRequired[Optional[float]]
    topP: NotRequired[Optional[float]]
    topK: NotRequired[Optional[float]]


class ModelParams4(TypedDict):
    use_cache: NotRequired[Optional[bool]]
    temperature: NotRequired[Optional[float]]
    topK: NotRequired[Optional[float]]


class ModelParams5(TypedDict):
    use_cache: NotRequired[Optional[bool]]


ModelParams = Union[ModelParams1, ModelParams2, ModelParams3, ModelParams4, ModelParams5]


class PromptOptions(TypedDict):
    model: NotRequired[Optional[str]]
    params: NotRequired[Optional[ModelParams]]
    position: NotRequired[Optional[str]]


class BraintrustAttachmentReference(TypedDict):
    type: Literal["braintrust_attachment"]
    """
    An identifier to help disambiguate parsing.
    """
    filename: str
    """
    Human-readable filename for user interfaces. Not related to attachment storage.
    """
    content_type: str
    """
    MIME type of this file.
    """
    key: str
    """
    Key in the object store bucket for this attachment.
    """


class ExternalAttachmentReference(TypedDict):
    type: Literal["external_attachment"]
    """
    An identifier to help disambiguate parsing.
    """
    filename: str
    """
    Human-readable filename for user interfaces. Not related to attachment storage.
    """
    content_type: str
    """
    MIME type of this file.
    """
    url: str
    """
    Fully qualified URL to the object in the external object store.
    """


AttachmentReference = Union[BraintrustAttachmentReference, ExternalAttachmentReference]


UploadStatus = Literal["uploading", "done", "error"]


class AttachmentStatus(TypedDict):
    upload_status: UploadStatus
    error_message: NotRequired[Optional[str]]
    """
    Describes the error encountered while uploading.
    """


IfExists = Literal["error", "ignore", "replace"]


class SavedFunctionId1(TypedDict):
    type: Literal["function"]
    id: str


class SavedFunctionId2(TypedDict):
    type: Literal["global"]
    name: str


SavedFunctionId = Union[SavedFunctionId1, SavedFunctionId2]


class Function1(TypedDict):
    name: str
    description: NotRequired[Optional[str]]
    parameters: NotRequired[Optional[Mapping[str, Any]]]
    strict: NotRequired[Optional[bool]]


class ToolFunctionDefinition(TypedDict):
    type: Literal["function"]
    function: Function1


class CacheControl(TypedDict):
    type: Literal["ephemeral"]


class ChatCompletionContentPartText(TypedDict):
    text: NotRequired[Optional[str]]
    type: Literal["text"]
    cache_control: NotRequired[Optional[CacheControl]]


class ImageUrl(TypedDict):
    url: str
    detail: NotRequired[Optional[Union[Literal["auto"], Literal["low"], Literal["high"]]]]


class ChatCompletionContentPartImage(TypedDict):
    image_url: ImageUrl
    type: Literal["image_url"]


ChatCompletionContentPart = Union[ChatCompletionContentPartText, ChatCompletionContentPartImage]


ChatCompletionContent = Union[str, Sequence[ChatCompletionContentPart]]


class Function2(TypedDict):
    arguments: str
    name: str


class ChatCompletionMessageToolCall(TypedDict):
    id: str
    function: Function2
    type: Literal["function"]


class ChatCompletionMessageReasoning(TypedDict):
    id: NotRequired[Optional[str]]
    content: NotRequired[Optional[str]]


class ChatCompletionMessageParam1(TypedDict):
    content: NotRequired[Optional[Union[str, Sequence[ChatCompletionContentPartText]]]]
    role: Literal["system"]
    name: NotRequired[Optional[str]]


class ChatCompletionMessageParam2(TypedDict):
    content: NotRequired[Optional[ChatCompletionContent]]
    role: Literal["user"]
    name: NotRequired[Optional[str]]


class FunctionCall1(TypedDict):
    arguments: str
    name: str


class ChatCompletionMessageParam3(TypedDict):
    role: Literal["assistant"]
    content: NotRequired[Optional[Union[str, Sequence[ChatCompletionContentPartText]]]]
    function_call: NotRequired[Optional[FunctionCall1]]
    name: NotRequired[Optional[str]]
    tool_calls: NotRequired[Optional[Sequence[ChatCompletionMessageToolCall]]]
    reasoning: NotRequired[Optional[Sequence[ChatCompletionMessageReasoning]]]


class ChatCompletionMessageParam4(TypedDict):
    content: NotRequired[Optional[Union[str, Sequence[ChatCompletionContentPartText]]]]
    role: Literal["tool"]
    tool_call_id: NotRequired[Optional[str]]


class ChatCompletionMessageParam5(TypedDict):
    content: Optional[str]
    name: str
    role: Literal["function"]


class ChatCompletionMessageParam6(TypedDict):
    content: NotRequired[Optional[Union[str, Sequence[ChatCompletionContentPartText]]]]
    role: Literal["developer"]
    name: NotRequired[Optional[str]]


class ChatCompletionMessageParam7(TypedDict):
    role: Literal["model"]
    content: NotRequired[Optional[str]]


ChatCompletionMessageParam = Union[
    ChatCompletionMessageParam1,
    ChatCompletionMessageParam2,
    ChatCompletionMessageParam3,
    ChatCompletionMessageParam4,
    ChatCompletionMessageParam5,
    ChatCompletionMessageParam6,
    ChatCompletionMessageParam7,
]


class Prompt(TypedDict):
    type: Literal["completion"]
    content: str


class Prompt1(TypedDict):
    type: Literal["chat"]
    messages: Sequence[ChatCompletionMessageParam]
    tools: NotRequired[Optional[str]]


class Parser(TypedDict):
    type: Literal["llm_classifier"]
    use_cot: bool
    choice_scores: Mapping[str, float]


class Origin(TypedDict):
    prompt_id: NotRequired[Optional[str]]
    project_id: NotRequired[Optional[str]]
    prompt_version: NotRequired[Optional[str]]


class PromptData(TypedDict):
    prompt: NotRequired[Optional[Union[Prompt, Prompt1]]]
    options: NotRequired[Optional[PromptOptions]]
    parser: NotRequired[Optional[Parser]]
    tool_functions: NotRequired[Optional[Sequence[SavedFunctionId]]]
    origin: NotRequired[Optional[Origin]]


__all__ = []
