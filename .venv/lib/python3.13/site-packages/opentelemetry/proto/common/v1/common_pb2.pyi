"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
Copyright 2019, OpenTelemetry Authors

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

import builtins
import collections.abc
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.message
import sys

import typing as typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing_extensions.final
class AnyValue(google.protobuf.message.Message):
    """AnyValue is used to represent any type of attribute value. AnyValue may contain a
    primitive value such as a string or integer or it may contain an arbitrary nested
    object containing arrays, key-value lists and primitives.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    STRING_VALUE_FIELD_NUMBER: builtins.int
    BOOL_VALUE_FIELD_NUMBER: builtins.int
    INT_VALUE_FIELD_NUMBER: builtins.int
    DOUBLE_VALUE_FIELD_NUMBER: builtins.int
    ARRAY_VALUE_FIELD_NUMBER: builtins.int
    KVLIST_VALUE_FIELD_NUMBER: builtins.int
    BYTES_VALUE_FIELD_NUMBER: builtins.int
    string_value: builtins.str
    bool_value: builtins.bool
    int_value: builtins.int
    double_value: builtins.float
    @property
    def array_value(self) -> global___ArrayValue: ...
    @property
    def kvlist_value(self) -> global___KeyValueList: ...
    bytes_value: builtins.bytes
    def __init__(
        self,
        *,
        string_value: builtins.str = ...,
        bool_value: builtins.bool = ...,
        int_value: builtins.int = ...,
        double_value: builtins.float = ...,
        array_value: global___ArrayValue | None = ...,
        kvlist_value: global___KeyValueList | None = ...,
        bytes_value: builtins.bytes = ...,
    ) -> None: ...
    def HasField(
        self,
        field_name: typing_extensions.Literal[
            "array_value",
            b"array_value",
            "bool_value",
            b"bool_value",
            "bytes_value",
            b"bytes_value",
            "double_value",
            b"double_value",
            "int_value",
            b"int_value",
            "kvlist_value",
            b"kvlist_value",
            "string_value",
            b"string_value",
            "value",
            b"value",
        ],
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "array_value",
            b"array_value",
            "bool_value",
            b"bool_value",
            "bytes_value",
            b"bytes_value",
            "double_value",
            b"double_value",
            "int_value",
            b"int_value",
            "kvlist_value",
            b"kvlist_value",
            "string_value",
            b"string_value",
            "value",
            b"value",
        ],
    ) -> None: ...
    def WhichOneof(
        self, oneof_group: typing_extensions.Literal["value", b"value"]
    ) -> (
        typing_extensions.Literal[
            "string_value",
            "bool_value",
            "int_value",
            "double_value",
            "array_value",
            "kvlist_value",
            "bytes_value",
        ]
        | None
    ): ...

global___AnyValue = AnyValue

@typing_extensions.final
class ArrayValue(google.protobuf.message.Message):
    """ArrayValue is a list of AnyValue messages. We need ArrayValue as a message
    since oneof in AnyValue does not allow repeated fields.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VALUES_FIELD_NUMBER: builtins.int
    @property
    def values(
        self,
    ) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[
        global___AnyValue
    ]:
        """Array of values. The array may be empty (contain 0 elements)."""

    def __init__(
        self,
        *,
        values: collections.abc.Iterable[global___AnyValue] | None = ...,
    ) -> None: ...
    def ClearField(
        self, field_name: typing_extensions.Literal["values", b"values"]
    ) -> None: ...

global___ArrayValue = ArrayValue

@typing_extensions.final
class KeyValueList(google.protobuf.message.Message):
    """KeyValueList is a list of KeyValue messages. We need KeyValueList as a message
    since `oneof` in AnyValue does not allow repeated fields. Everywhere else where we need
    a list of KeyValue messages (e.g. in Span) we use `repeated KeyValue` directly to
    avoid unnecessary extra wrapping (which slows down the protocol). The 2 approaches
    are semantically equivalent.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VALUES_FIELD_NUMBER: builtins.int
    @property
    def values(
        self,
    ) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[
        global___KeyValue
    ]:
        """A collection of key/value pairs of key-value pairs. The list may be empty (may
        contain 0 elements).
        The keys MUST be unique (it is not allowed to have more than one
        value with the same key).
        """

    def __init__(
        self,
        *,
        values: collections.abc.Iterable[global___KeyValue] | None = ...,
    ) -> None: ...
    def ClearField(
        self, field_name: typing_extensions.Literal["values", b"values"]
    ) -> None: ...

global___KeyValueList = KeyValueList

@typing_extensions.final
class KeyValue(google.protobuf.message.Message):
    """KeyValue is a key-value pair that is used to store Span attributes, Link
    attributes, etc.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    KEY_FIELD_NUMBER: builtins.int
    VALUE_FIELD_NUMBER: builtins.int
    key: builtins.str
    @property
    def value(self) -> global___AnyValue: ...
    def __init__(
        self,
        *,
        key: builtins.str = ...,
        value: global___AnyValue | None = ...,
    ) -> None: ...
    def HasField(
        self, field_name: typing_extensions.Literal["value", b"value"]
    ) -> builtins.bool: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "key", b"key", "value", b"value"
        ],
    ) -> None: ...

global___KeyValue = KeyValue

@typing_extensions.final
class InstrumentationScope(google.protobuf.message.Message):
    """InstrumentationScope is a message representing the instrumentation scope information
    such as the fully qualified name and version.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAME_FIELD_NUMBER: builtins.int
    VERSION_FIELD_NUMBER: builtins.int
    ATTRIBUTES_FIELD_NUMBER: builtins.int
    DROPPED_ATTRIBUTES_COUNT_FIELD_NUMBER: builtins.int
    name: builtins.str
    """An empty instrumentation scope name means the name is unknown."""
    version: builtins.str
    @property
    def attributes(
        self,
    ) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[
        global___KeyValue
    ]:
        """Additional attributes that describe the scope. [Optional].
        Attribute keys MUST be unique (it is not allowed to have more than one
        attribute with the same key).
        """
    dropped_attributes_count: builtins.int
    def __init__(
        self,
        *,
        name: builtins.str = ...,
        version: builtins.str = ...,
        attributes: collections.abc.Iterable[global___KeyValue] | None = ...,
        dropped_attributes_count: builtins.int = ...,
    ) -> None: ...
    def ClearField(
        self,
        field_name: typing_extensions.Literal[
            "attributes",
            b"attributes",
            "dropped_attributes_count",
            b"dropped_attributes_count",
            "name",
            b"name",
            "version",
            b"version",
        ],
    ) -> None: ...

global___InstrumentationScope = InstrumentationScope
